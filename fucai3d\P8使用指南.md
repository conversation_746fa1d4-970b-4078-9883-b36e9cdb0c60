# P8智能交集融合系统使用指南

## 📋 目录

1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [命令行工具](#命令行工具)
4. [API使用](#api使用)
5. [配置说明](#配置说明)
6. [性能监控](#性能监控)
7. [故障排除](#故障排除)

## 🎯 系统概述

P8智能交集融合系统是一个先进的福彩3D预测融合平台，集成了以下核心功能：

- **多预测器融合**: 整合P3-P7所有预测器的结果
- **智能排序算法**: 5种排序策略，自适应选择最优方案
- **动态权重调整**: 基于历史性能自动优化权重
- **约束优化**: 数学约束确保预测合理性
- **性能监控**: 实时监控系统性能和预测效果
- **自动化报告**: 生成详细的性能分析报告

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的依赖：

```bash
# 安装Python依赖
pip install numpy pandas sqlite3 pyyaml matplotlib seaborn psutil

# 创建必要目录
mkdir -p data logs reports config
```

### 2. 数据库初始化

```bash
# 创建融合系统数据表
python create_fusion_db.py
```

### 3. 第一次预测

```bash
# 预测下一期号码
python p8_fusion_cli.py predict --issue 2024100

# 查看系统状态
python p8_fusion_cli.py status
```

## 🛠️ 命令行工具

P8系统提供了完整的命令行接口 `p8_fusion_cli.py`：

### 预测命令

```bash
# 基本预测
python p8_fusion_cli.py predict --issue 2024100

# 指定融合方法和排序策略
python p8_fusion_cli.py predict --issue 2024100 \
    --method adaptive_fusion \
    --strategy adaptive \
    --top-k 20

# 保存结果到文件
python p8_fusion_cli.py predict --issue 2024100 \
    --output predictions/2024100.json
```

**可用的融合方法**:
- `adaptive_fusion`: 自适应融合（推荐）
- `weighted_average`: 加权平均融合
- `bayesian_fusion`: 贝叶斯融合

**可用的排序策略**:
- `adaptive`: 自适应排序（推荐）
- `probability_first`: 概率优先排序
- `constraint_first`: 约束优先排序

### 评估命令

```bash
# 评估预测结果
python p8_fusion_cli.py evaluate --issue 2024099 --actual 123

# 评估并更新权重
python p8_fusion_cli.py evaluate --issue 2024099 --actual 123 --update-weights
```

### 报告生成

```bash
# 生成30天性能报告
python p8_fusion_cli.py report --days 30

# 生成报告到指定目录，不包含图表
python p8_fusion_cli.py report --days 7 \
    --output-dir custom_reports \
    --no-charts
```

### 性能监控

```bash
# 启动实时监控（按Ctrl+C停止）
python p8_fusion_cli.py monitor --start --interval 60

# 查看监控状态
python p8_fusion_cli.py monitor --status
```

### 权重管理

```bash
# 查看当前权重
python p8_fusion_cli.py weights --show

# 重置权重到默认值
python p8_fusion_cli.py weights --reset

# 优化融合参数
python p8_fusion_cli.py weights --optimize
```

### 系统测试

```bash
# 运行集成测试
python p8_fusion_cli.py test --integration

# 运行性能基准测试
python p8_fusion_cli.py test --benchmark

# 验证系统配置
python p8_fusion_cli.py test --validate
```

## 💻 API使用

### 基本使用

```python
from src.fusion.fusion_predictor import FusionPredictor

# 初始化融合预测器
predictor = FusionPredictor(
    db_path="data/lottery.db",
    config_path="config/fusion_config.yaml"
)

# 预测下一期
result = predictor.predict_next_period(
    issue="2024100",
    fusion_method="adaptive_fusion",
    ranking_strategy="adaptive",
    top_k=10
)

# 输出预测结果
for pred in result['predictions']:
    print(f"排名{pred['rank']}: {pred['combination']} "
          f"(概率: {pred['combined_probability']:.4f})")
```

### 批量预测

```python
# 批量预测多个期号
issues = ["2024100", "2024101", "2024102"]
batch_results = predictor.predict_batch(
    issues=issues,
    fusion_method="adaptive_fusion",
    top_k=5
)

for issue, result in batch_results.items():
    if 'error' not in result:
        print(f"期号 {issue}: {len(result['predictions'])} 个预测")
```

### 性能评估

```python
# 评估预测结果并更新权重
evaluation_result = predictor.evaluate_and_update_weights(
    actual_result="123",
    issue="2024099"
)

print(f"命中率: {evaluation_result['performance_summary']['exact_hit_rate']:.2%}")
```

### 系统监控

```python
from src.fusion.performance_monitor import PerformanceMonitor

# 初始化性能监控
monitor = PerformanceMonitor(
    db_path="data/lottery.db",
    config={"monitoring_interval": 60}
)

# 启动监控
monitor.start_monitoring(interval=60)

# 添加告警回调
def alert_callback(alert_data):
    print(f"告警: {alert_data['rule_name']}")

monitor.add_alert_callback(alert_callback)
```

## ⚙️ 配置说明

主配置文件 `config/fusion_config.yaml`：

```yaml
# 融合引擎配置
fusion:
  default_method: "adaptive_fusion"
  probability_weight: 0.5      # 概率权重
  constraint_weight: 0.3       # 约束权重
  diversity_weight: 0.2        # 多样性权重
  min_probability: 1.0e-6      # 最小概率阈值
  max_recommendations: 20      # 最大推荐数量

# 排序器配置
ranking:
  default_strategy: "adaptive"
  top_k: 20                    # Top-K数量
  min_score_threshold: 0.01    # 最小分数阈值
  diversity_penalty: 0.1       # 多样性惩罚

# 权重调整器配置
weights:
  learning_rate: 0.1           # 学习率
  decay_factor: 0.95           # 衰减因子
  min_weight: 0.1              # 最小权重
  max_weight: 2.0              # 最大权重
  evaluation_window: 30        # 评估窗口（天）

# 约束优化器配置
constraints:
  sum_tolerance: 2.0           # 和值容差
  span_tolerance: 1.0          # 跨度容差
  consistency_weight: 0.3      # 一致性权重
  diversity_weight: 0.2        # 多样性权重

# 数据库配置
database:
  path: "data/lottery.db"
  timeout: 30

# 日志配置
logging:
  level: "INFO"
  file: "logs/fusion_system.log"
```

### 关键参数说明

**融合权重**:
- `probability_weight`: 概率融合的权重，值越大越重视概率
- `constraint_weight`: 约束优化的权重，值越大越重视约束
- `diversity_weight`: 多样性的权重，值越大结果越多样化

**学习参数**:
- `learning_rate`: 权重调整的学习率，值越大调整越激进
- `evaluation_window`: 性能评估的时间窗口，单位为天

## 📊 性能监控

### 监控指标

P8系统监控以下关键指标：

1. **预测性能**:
   - 成功率、错误率
   - 平均执行时间
   - 预测频率

2. **命中率**:
   - 精确命中率
   - Top-K命中率
   - 位置命中率

3. **系统资源**:
   - CPU使用率
   - 内存使用率
   - 数据库性能

4. **融合质量**:
   - 置信度准确性
   - 权重稳定性
   - 约束一致性

### 告警规则

系统内置以下告警规则：

- 成功率低于80%
- 执行时间超过5秒
- 命中率低于10%
- 内存使用超过80%
- 数据库连接错误

### 自动调整

系统会根据性能指标自动调整：

- **低命中率**: 增加约束权重
- **高错误率**: 重置权重到默认值
- **置信度不准**: 调整置信度阈值
- **权重失衡**: 平衡权重分布
- **性能下降**: 触发参数优化

## 🔧 故障排除

### 常见问题

**1. 数据库连接失败**
```bash
# 检查数据库文件是否存在
ls -la data/lottery.db

# 重新创建数据库表
python create_fusion_db.py
```

**2. 预测器加载失败**
```bash
# 检查预测器状态
python p8_fusion_cli.py status

# 验证系统配置
python p8_fusion_cli.py test --validate
```

**3. 内存使用过高**
```bash
# 运行性能基准测试
python p8_fusion_cli.py test --benchmark

# 调整配置参数
# 减少 max_recommendations 和 top_k 值
```

**4. 预测结果异常**
```bash
# 重置权重
python p8_fusion_cli.py weights --reset

# 优化参数
python p8_fusion_cli.py weights --optimize
```

### 日志分析

查看系统日志：

```bash
# 查看最新日志
tail -f logs/fusion_system.log

# 搜索错误信息
grep "ERROR" logs/fusion_system.log

# 查看性能统计
grep "性能" logs/fusion_system.log
```

### 性能优化

**1. 数据库优化**
- 定期清理历史数据
- 重建数据库索引
- 调整数据库缓存大小

**2. 内存优化**
- 减少缓存大小
- 调整评估窗口
- 限制并发数量

**3. 算法优化**
- 选择合适的融合方法
- 调整权重学习率
- 优化约束参数

## 📈 最佳实践

1. **定期评估**: 每天评估预测结果并更新权重
2. **监控告警**: 启用性能监控和告警机制
3. **参数调优**: 根据实际效果调整配置参数
4. **数据备份**: 定期备份数据库和配置文件
5. **版本管理**: 记录重要的配置变更和性能数据

## 🎯 进阶使用

### 自定义融合算法

```python
# 扩展融合引擎
from src.fusion.probability_fusion_engine import ProbabilityFusionEngine

class CustomFusionEngine(ProbabilityFusionEngine):
    def custom_fusion_method(self, predictions, weights):
        # 实现自定义融合逻辑
        pass
```

### 自定义排序策略

```python
# 扩展智能排序器
from src.fusion.intelligent_ranker import IntelligentRanker

class CustomRanker(IntelligentRanker):
    def custom_ranking_strategy(self, combinations, constraint_info):
        # 实现自定义排序逻辑
        pass
```

### 集成外部系统

```python
# 集成到Web应用
from flask import Flask, jsonify
from src.fusion.fusion_predictor import FusionPredictor

app = Flask(__name__)
predictor = FusionPredictor("data/lottery.db")

@app.route('/predict/<issue>')
def predict_api(issue):
    result = predictor.predict_next_period(issue)
    return jsonify(result)
```

---

**P8智能交集融合系统** - 让福彩3D预测更智能、更准确！

如有问题，请查看日志文件或运行系统诊断命令。
