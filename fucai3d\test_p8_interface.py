#!/usr/bin/env python3
"""
P8接口快速测试脚本

验证P8融合系统所需的接口功能
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def test_sum_predictor_interface():
    """测试P6和值预测器的新接口"""
    print("=== 测试P6和值预测器新接口 ===")
    
    try:
        from src.predictors.sum_predictor import SumPredictor
        
        # 初始化预测器
        predictor = SumPredictor("data/lottery.db")
        
        # 检查新方法是否存在
        has_predict_probability = hasattr(predictor, 'predict_probability')
        has_get_constraint_info = hasattr(predictor, 'get_constraint_info')
        
        print(f"predict_probability方法: {'✓' if has_predict_probability else '✗'}")
        print(f"get_constraint_info方法: {'✓' if has_get_constraint_info else '✗'}")
        
        if has_predict_probability and has_get_constraint_info:
            print("✓ P6和值预测器接口补充成功")
            return True
        else:
            print("✗ P6和值预测器接口不完整")
            return False
            
    except Exception as e:
        print(f"测试P6和值预测器接口失败: {e}")
        return False

def test_unified_interface():
    """测试统一预测器接口"""
    print("\n=== 测试统一预测器接口 ===")
    
    try:
        from src.predictors.unified_predictor_interface import UnifiedPredictorInterface
        
        # 初始化接口
        interface = UnifiedPredictorInterface("data/lottery.db")
        
        # 检查关键方法
        methods = [
            'load_all_predictors',
            'get_all_predictions', 
            'get_constraint_matrix',
            'validate_predictions',
            'get_predictor_status'
        ]
        
        for method in methods:
            has_method = hasattr(interface, method)
            print(f"{method}: {'✓' if has_method else '✗'}")
        
        print("✓ 统一预测器接口创建成功")
        return True
        
    except Exception as e:
        print(f"测试统一预测器接口失败: {e}")
        return False

def test_data_format_standard():
    """测试数据格式标准化"""
    print("\n=== 测试数据格式标准化 ===")
    
    try:
        from src.predictors.data_format_standard import DataFormatStandardizer, StandardPredictionResult
        
        # 初始化标准化器
        standardizer = DataFormatStandardizer()
        
        # 测试标准化方法
        methods = [
            'standardize_position_prediction',
            'standardize_sum_prediction',
            'standardize_span_prediction',
            'validate_standard_result'
        ]
        
        for method in methods:
            has_method = hasattr(standardizer, method)
            print(f"{method}: {'✓' if has_method else '✗'}")
        
        # 测试StandardPredictionResult
        try:
            result = StandardPredictionResult(
                predictor_name='test',
                prediction_type='position',
                predicted_value=5,
                probabilities=[0.1] * 10,
                confidence=0.8,
                constraint_info={},
                metadata={},
                timestamp='2024-01-01T00:00:00'
            )
            print("StandardPredictionResult: ✓")
        except Exception as e:
            print(f"StandardPredictionResult: ✗ ({e})")
        
        print("✓ 数据格式标准化模块创建成功")
        return True
        
    except Exception as e:
        print(f"测试数据格式标准化失败: {e}")
        return False

def test_interface_integration():
    """测试接口集成"""
    print("\n=== 测试接口集成 ===")
    
    try:
        # 测试模块导入
        from src.predictors.unified_predictor_interface import UnifiedPredictorInterface
        from src.predictors.data_format_standard import DataFormatStandardizer
        
        # 创建实例
        interface = UnifiedPredictorInterface("data/lottery.db")
        standardizer = DataFormatStandardizer()
        
        print("模块导入: ✓")
        print("实例创建: ✓")
        
        # 测试基本功能
        status = interface.get_predictor_status()
        print(f"预测器状态获取: ✓ (发现 {len(status)} 个预测器)")
        
        print("✓ 接口集成测试通过")
        return True
        
    except Exception as e:
        print(f"接口集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始P8接口测试...")
    
    tests = [
        test_sum_predictor_interface,
        test_unified_interface,
        test_data_format_standard,
        test_interface_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试执行失败: {e}")
            results.append(False)
    
    # 输出总结
    print(f"\n=== 测试总结 ===")
    print(f"总测试数: {len(tests)}")
    print(f"通过数: {sum(results)}")
    print(f"失败数: {len(results) - sum(results)}")
    
    if all(results):
        print("🎉 所有P8接口测试通过！")
        return True
    else:
        print("⚠ 部分P8接口测试失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
